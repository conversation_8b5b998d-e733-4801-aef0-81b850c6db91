# -*- coding: utf-8 -*-
"""
南京欧美同学会新闻分页爬虫
专门用于爬取本会动态页面的分页内容并输出为Excel格式
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html
import re
from datetime import datetime
import os
import sys
import time
from urllib.parse import urljoin
try:
    from openpyxl.styles import Alignment
except ImportError:
    Alignment = None

from config_news import (
    START_URL, BASE_URL, LINKS_XPATH, NEXT_PAGE_XPATH, MAX_PAGES, START_PAGE,
    OUTPUT_FILENAME, HEADERS, REQUEST_TIMEOUT, MAX_RETRIES, RETRY_DELAY,
    VERIFY_SSL, PROXIES, CONTENT_XPATHS, TITLE_XPATHS
)


class NewsSpider:
    """南京欧美同学会新闻分页爬虫类"""

    def __init__(self):
        """初始化爬虫"""
        self.start_url = START_URL
        self.base_url = BASE_URL
        self.links_xpath = LINKS_XPATH
        self.next_page_xpath = NEXT_PAGE_XPATH
        self.max_pages = MAX_PAGES
        self.start_page = START_PAGE
        self.headers = HEADERS
        self.timeout = REQUEST_TIMEOUT
        self.output_file = OUTPUT_FILENAME
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY
        self.verify_ssl = VERIFY_SSL
        self.proxies = PROXIES
        self.content_xpaths = CONTENT_XPATHS
        self.title_xpaths = TITLE_XPATHS
        
    def fetch_webpage(self, url):
        """
        获取网页内容（带重试机制）
        Args:
            url: 目标网址
        Returns:
            tuple: (是否成功, 网页内容/错误信息, 页面标题)
        """
        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    print(f"第 {attempt + 1} 次尝试...")
                    time.sleep(self.retry_delay)

                # 构建请求参数
                request_params = {
                    'headers': self.headers,
                    'timeout': self.timeout,
                    'verify': self.verify_ssl
                }

                # 代理设置
                if self.proxies:
                    request_params['proxies'] = self.proxies
                else:
                    request_params['proxies'] = {'http': None, 'https': None}

                response = requests.get(url, **request_params)
                response.raise_for_status()

                # 编码检测
                if response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'
                elif not response.encoding:
                    response.encoding = 'utf-8'

                # 获取页面标题
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.get_text().strip() if title else "未获取到标题"

                print(f"✓ 网页获取成功，页面标题: {page_title}")
                return True, response.text, page_title

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (超过{self.timeout}秒)"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except requests.exceptions.ConnectionError as e:
                error_msg = f"网络连接错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, "网络连接错误，请检查网络连接或防火墙设置", ""

            except requests.exceptions.HTTPError as e:
                error_msg = f"HTTP错误: {e}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

            except Exception as e:
                error_msg = f"获取网页时发生未知错误: {str(e)}"
                print(f"✗ 尝试 {attempt + 1}: {error_msg}")
                if attempt == self.max_retries - 1:
                    return False, error_msg, ""

        return False, "所有重试都失败了", ""

    def extract_links_from_xpath(self, html_content, xpath, base_url):
        """
        从指定XPath提取所有链接
        Args:
            html_content: HTML内容
            xpath: XPath路径
            base_url: 基础URL
        Returns:
            list: 链接列表
        """
        try:
            tree = html.fromstring(html_content)
            # 查找指定XPath下的所有链接
            link_elements = tree.xpath(f"{xpath}//a[@href]")

            links = []
            for link in link_elements:
                href = link.get('href')
                if href:
                    # 处理相对URL
                    full_url = urljoin(base_url, href)
                    # 获取链接文本作为标题
                    link_text = link.text_content().strip() if hasattr(link, 'text_content') else ""
                    if link_text and '/detail/' in full_url:  # 只获取详情页链接
                        links.append({
                            'url': full_url,
                            'title': link_text
                        })

            print(f"  从XPath提取到 {len(links)} 个有效链接")
            return links

        except Exception as e:
            print(f"  提取链接失败: {str(e)}")
            return []

    def extract_article_content(self, url):
        """
        提取文章详情页的标题和正文
        Args:
            url: 文章URL
        Returns:
            tuple: (标题, 正文内容)
        """
        print(f"  正在提取文章内容: {url}")

        # 获取文章页面
        success, html_content, _ = self.fetch_webpage(url)
        if not success:
            print(f"    获取文章页面失败: {html_content}")
            return "", ""

        try:
            tree = html.fromstring(html_content)

            # 提取标题
            title = ""
            for title_xpath in self.title_xpaths:
                title_elements = tree.xpath(title_xpath)
                if title_elements:
                    if title_xpath == "//title":
                        title = title_elements[0].text_content().strip()
                        # 清理标题中的网站名称
                        title = re.sub(r'[_\-|].*?南京欧美同学会.*?$', '', title).strip()
                    else:
                        title = title_elements[0].text_content().strip()
                    if title:
                        break

            # 提取正文内容
            content = ""
            for content_xpath in self.content_xpaths:
                content_elements = tree.xpath(content_xpath)
                if content_elements:
                    content_parts = []
                    for element in content_elements:
                        if hasattr(element, 'text_content'):
                            text = element.text_content()
                            content_parts.append(text)
                    content = '\n'.join(content_parts)
                    if content.strip():
                        break

            # 清理文本
            content = self.clean_text(content)

            print(f"    提取成功 - 标题: {title[:50]}{'...' if len(title) > 50 else ''}")
            print(f"    内容长度: {len(content)} 字符")

            return title, content

        except Exception as e:
            print(f"    提取文章内容失败: {str(e)}")
            return "", ""

    def clean_text(self, text):
        """清理文本内容"""
        if not text:
            return ""
        try:
            # 移除HTML标签和实体
            text = re.sub(r'<[^>]+>', '', text)
            text = text.replace('&nbsp;', ' ').replace('&lt;', '<').replace('&gt;', '>')
            text = text.replace('&amp;', '&').replace('&quot;', '"').replace('&#39;', "'")

            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'\n\s*\n', '\n\n', text)
            return text.strip()
        except:
            return text

    def find_next_page_url(self, html_content, current_url):
        """
        查找下一页URL
        Args:
            html_content: 当前页面HTML内容
            current_url: 当前页面URL
        Returns:
            str: 下一页URL，如果没有则返回None
        """
        try:
            tree = html.fromstring(html_content)

            # 尝试使用XPath查找下一页链接
            next_links = tree.xpath(self.next_page_xpath)
            if next_links:
                next_url = urljoin(current_url, next_links[0])
                print(f"  找到下一页链接: {next_url}")
                return next_url

            # 备用方案：根据URL模式构造下一页URL
            # 当前URL格式: /news/7/0.html, /news/7/1.html 等
            url_parts = current_url.split('/')
            if len(url_parts) >= 2:
                try:
                    current_page_file = url_parts[-1]  # 例如: "0.html"
                    current_page_num = int(current_page_file.split('.')[0])
                    next_page_num = current_page_num + 1

                    # 构造下一页URL
                    next_page_file = f"{next_page_num}.html"
                    url_parts[-1] = next_page_file
                    next_url = '/'.join(url_parts)

                    print(f"  根据URL模式构造下一页: {next_url}")
                    return next_url
                except (ValueError, IndexError):
                    pass

            print("  未找到下一页链接")
            return None

        except Exception as e:
            print(f"  查找下一页链接失败: {str(e)}")
            return None

    def crawl_page_links(self, page_url):
        """
        爬取单页的所有文章链接和内容
        Args:
            page_url: 页面URL
        Returns:
            list: 文章数据列表
        """
        print(f"\n正在爬取页面: {page_url}")

        # 获取列表页面
        success, html_content, page_title = self.fetch_webpage(page_url)
        if not success:
            print(f"获取页面失败: {html_content}")
            return []

        # 提取页面中的所有文章链接
        links = self.extract_links_from_xpath(html_content, self.links_xpath, self.base_url)
        if not links:
            print("未找到任何文章链接")
            return []

        print(f"找到 {len(links)} 个文章链接，开始提取内容...")

        articles = []
        for i, link in enumerate(links, 1):
            print(f"\n[{i}/{len(links)}] 处理文章: {link['title']}")

            # 提取文章内容
            title, content = self.extract_article_content(link['url'])

            if title and content:
                articles.append({
                    'title': title,
                    'content': content,
                    'url': link['url'],
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                print(f"    ✓ 成功提取")
            else:
                print(f"    ✗ 提取失败")

            # 添加延迟避免请求过快
            time.sleep(1)

        print(f"\n页面爬取完成，成功提取 {len(articles)} 篇文章")
        return articles

    def save_to_excel(self, all_articles):
        """
        保存所有结果到Excel文件（两列格式）
        Args:
            all_articles: 所有文章数据列表
        Returns:
            bool: 是否保存成功
        """
        try:
            # 检查文件是否被占用
            if os.path.exists(self.output_file):
                try:
                    temp_name = self.output_file + '.tmp'
                    os.rename(self.output_file, temp_name)
                    os.rename(temp_name, self.output_file)
                except OSError:
                    print(f"文件被占用，请关闭Excel文件: {self.output_file}")
                    return False

            # 准备数据
            data = {
                '标题': [article['title'] for article in all_articles],
                '正文内容': [article['content'] for article in all_articles]
            }

            df = pd.DataFrame(data)

            # 保存到Excel
            with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='本会动态', index=False)

                # 调整列宽
                worksheet = writer.sheets['本会动态']
                worksheet.column_dimensions['A'].width = 50  # 标题列
                worksheet.column_dimensions['B'].width = 100  # 内容列

                # 设置自动换行
                if Alignment:
                    for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
                        for cell in row:
                            cell.alignment = Alignment(wrap_text=True, vertical='top')

            print(f"✓ 数据已保存到: {self.output_file}")
            return True

        except Exception as e:
            print(f"保存Excel失败: {str(e)}")
            return False

    def run_paginated_crawl(self):
        """
        执行分页爬取主逻辑
        Returns:
            bool: 是否成功完成爬取
        """
        print("南京欧美同学会新闻分页爬虫启动")
        print(f"起始URL: {self.start_url}")
        print(f"计划爬取页数: {self.max_pages}")

        all_articles = []
        current_url = self.start_url
        page_count = 0

        while current_url and page_count < self.max_pages:
            page_count += 1
            print(f"\n{'='*60}")
            print(f"正在爬取第 {page_count}/{self.max_pages} 页")
            print(f"{'='*60}")

            # 爬取当前页面的所有文章
            articles = self.crawl_page_links(current_url)
            if articles:
                all_articles.extend(articles)
                print(f"第 {page_count} 页成功提取 {len(articles)} 篇文章")
            else:
                print(f"第 {page_count} 页未提取到任何文章")

            # 如果还没达到最大页数，查找下一页
            if page_count < self.max_pages:
                # 重新获取当前页面来查找下一页链接
                success, html_content, _ = self.fetch_webpage(current_url)
                if success:
                    next_url = self.find_next_page_url(html_content, current_url)
                    if next_url:
                        current_url = next_url
                        print(f"准备爬取下一页: {next_url}")
                    else:
                        print("未找到下一页链接，爬取结束")
                        break
                else:
                    print("获取当前页面失败，无法查找下一页")
                    break

            # 添加页面间延迟
            if page_count < self.max_pages:
                print("等待2秒后继续...")
                time.sleep(2)

        # 保存结果
        print(f"\n{'='*60}")
        print("爬取完成，开始保存数据...")
        print(f"总共爬取了 {page_count} 页，提取了 {len(all_articles)} 篇文章")

        if all_articles:
            if self.save_to_excel(all_articles):
                print(f"✓ 爬取任务完成！数据已保存到: {self.output_file}")
                return True
            else:
                print("✗ 数据保存失败")
                return False
        else:
            print("✗ 未提取到任何文章数据")
            return False


def main():
    """主函数"""
    try:
        spider = NewsSpider()
        success = spider.run_paginated_crawl()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n错误: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
